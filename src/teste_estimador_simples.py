#!/usr/bin/env python3
"""
Teste simples do estimador XGBoost
Usa apenas algumas ações para validar a implementação
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import xgboost as xgb
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings

warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment
from features_xgboost import calcular_features_e_sinais

def criar_dados_teste():
    """
    Cria dados sintéticos para teste do estimador
    """
    # Criar dados sintéticos de uma ação
    dates = pd.date_range(start='2020-01-01', end='2025-07-18', freq='D')
    dates = dates[dates.weekday < 5]  # Apenas dias úteis
    
    np.random.seed(42)
    n_days = len(dates)
    
    # Simular preços com tendência e volatilidade
    base_price = 100
    returns = np.random.normal(0.001, 0.02, n_days)  # Retornos diários
    prices = [base_price]
    
    for i in range(1, n_days):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(max(new_price, 1))  # Preço mínimo de 1
    
    # Criar DataFrame com dados OHLCV
    dados = pd.DataFrame(index=dates)
    dados['Close'] = prices
    dados['Open'] = dados['Close'].shift(1) * (1 + np.random.normal(0, 0.005, n_days))
    dados['High'] = np.maximum(dados['Open'], dados['Close']) * (1 + np.abs(np.random.normal(0, 0.01, n_days)))
    dados['Low'] = np.minimum(dados['Open'], dados['Close']) * (1 - np.abs(np.random.normal(0, 0.01, n_days)))
    dados['Volume'] = np.random.lognormal(15, 0.5, n_days)  # Volume log-normal
    
    # Preencher NaN
    dados = dados.fillna(method='ffill').fillna(method='bfill')
    
    return dados

def teste_features():
    """
    Testa o cálculo de features
    """
    print("🧪 Testando cálculo de features...")
    
    # Criar dados de teste
    dados = criar_dados_teste()
    print(f"   • Dados criados: {len(dados)} dias")
    
    # Calcular features
    try:
        dados_com_features = calcular_features_e_sinais(dados)
        print(f"   • Features calculadas: {len(dados_com_features.columns)} colunas")
        print(f"   • Dados válidos: {len(dados_com_features)} dias")
        
        # Verificar features essenciais
        features_essenciais = ['Media_OHLC', 'Media_OHLC_PctChange', 'Volume', 'Spread', 'Volatilidade']
        for feature in features_essenciais:
            if feature in dados_com_features.columns:
                print(f"   ✅ {feature}: OK")
            else:
                print(f"   ❌ {feature}: FALTANDO")
        
        return dados_com_features
        
    except Exception as e:
        print(f"   ❌ Erro no cálculo de features: {e}")
        return None

def teste_estimador_simples(dados_com_features):
    """
    Testa o estimador XGBoost com dados simples
    """
    print("\n🚀 Testando estimador XGBoost...")
    
    if dados_com_features is None:
        print("   ❌ Dados não disponíveis")
        return
    
    try:
        # Preparar features básicas (apenas algumas para teste)
        feature_cols = [
            'Media_OHLC_PctChange_Lag_1', 'Media_OHLC_PctChange_Lag_2', 'Media_OHLC_PctChange_Lag_3',
            'Volume_Lag_1', 'Spread_Lag_1', 'Volatilidade_Lag_1',
            'Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta'
        ]
        
        # Verificar se features existem
        features_disponiveis = [col for col in feature_cols if col in dados_com_features.columns]
        print(f"   • Features disponíveis: {len(features_disponiveis)}/{len(feature_cols)}")
        
        if len(features_disponiveis) < 5:
            print("   ❌ Poucas features disponíveis para teste")
            return
        
        # Preparar dados
        X = dados_com_features[features_disponiveis].dropna()
        y = dados_com_features.loc[X.index, 'Media_OHLC']
        
        print(f"   • Dados para treinamento: {len(X)} registros")
        
        if len(X) < 100:
            print("   ❌ Poucos dados para treinamento")
            return
        
        # Dividir dados (80% treino, 20% teste)
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        print(f"   • Treino: {len(X_train)} registros")
        print(f"   • Teste: {len(X_test)} registros")
        
        # Normalizar features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Treinar modelo
        modelo = xgb.XGBRegressor(
            objective='reg:squarederror',
            n_estimators=50,  # Reduzido para teste
            max_depth=4,      # Reduzido para teste
            learning_rate=0.1,
            random_state=42
        )
        
        modelo.fit(X_train_scaled, y_train)
        print("   ✅ Modelo treinado")
        
        # Fazer predições
        y_pred = modelo.predict(X_test_scaled)
        
        # Calcular métricas
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        print(f"   📊 Métricas:")
        print(f"      • RMSE: {rmse:.4f}")
        print(f"      • R²: {r2:.4f}")
        
        # Testar geração de sinais
        print("\n🎯 Testando geração de sinais...")
        
        # Usar último valor para teste
        ultimo_valor_real = y_test.iloc[-1]
        ultimo_valor_estimado = y_pred[-1]
        
        if len(y_test) >= 2:
            valor_anterior = y_test.iloc[-2]
        else:
            valor_anterior = ultimo_valor_real
        
        # Calcular variação percentual
        if valor_anterior > 0:
            pct_change_real = (ultimo_valor_real - valor_anterior) / valor_anterior * 100
            pct_change_estimado = (ultimo_valor_estimado - valor_anterior) / valor_anterior * 100
        else:
            pct_change_real = 0
            pct_change_estimado = 0
        
        threshold = config.get('xgboost.features.pct_threshold', 0.5)
        
        # Gerar sinais
        if pct_change_estimado > threshold:
            sinal = "COMPRA"
        elif pct_change_estimado < -threshold:
            sinal = "VENDA"
        else:
            sinal = "SEM_SINAL"
        
        print(f"   • Valor anterior: {valor_anterior:.2f}")
        print(f"   • Valor real: {ultimo_valor_real:.2f} ({pct_change_real:+.2f}%)")
        print(f"   • Valor estimado: {ultimo_valor_estimado:.2f} ({pct_change_estimado:+.2f}%)")
        print(f"   • Threshold: {threshold}%")
        print(f"   • Sinal gerado: {sinal}")

        # Testar formato de saída similar ao classificador
        print(f"\n🎯 Teste do formato de saída:")
        dados_teste = dados_com_features.copy()
        dados_teste['Valor_Estimado'] = ultimo_valor_estimado
        dados_teste['Pct_Change_Estimado'] = pct_change_estimado
        dados_teste['Pred_Compra'] = 1 if sinal == "COMPRA" else 0
        dados_teste['Pred_Venda'] = 1 if sinal == "VENDA" else 0
        dados_teste['Sinal_Tipo'] = sinal

        # Simular dados de uma ação para teste
        acoes_teste = {'TESTE3.SA': dados_teste}

        # Importar e testar função de impressão
        from estimador_xgboost_sinais import imprimir_recomendacoes_estimador
        imprimir_recomendacoes_estimador(acoes_teste)

        print("\n✅ Teste do estimador concluído com sucesso!")
        
    except Exception as e:
        print(f"   ❌ Erro no teste do estimador: {e}")
        import traceback
        traceback.print_exc()

def main():
    """
    Função principal do teste
    """
    print("🧪 TESTE SIMPLES DO ESTIMADOR XGBOOST")
    print("=" * 50)
    
    # Configurar ambiente
    setup_environment()
    
    # Testar features
    dados_com_features = teste_features()
    
    # Testar estimador
    if dados_com_features is not None:
        teste_estimador_simples(dados_com_features)
    
    print("\n🏁 Teste concluído!")

if __name__ == "__main__":
    main()
